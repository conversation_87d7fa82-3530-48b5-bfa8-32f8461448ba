<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Krypto Crypto</title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" sizes="32x32" href="favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="favicon-16x16.png">
    <link rel="apple-touch-icon" sizes="180x180" href="apple-touch-icon.png">
    <link rel="manifest" href="site.webmanifest">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary-color: #ff6b6b;
            --secondary-color: #1E1E1E;
            --accent-color: #E67E22;
            --background-color: #121212;
            --text-color: #E0E0E0;
            --card-bg: linear-gradient(145deg, #1A1A1A, #222222);
            --border-color: #333333;
        }

        body {
            font-family: 'Poppins', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #0A0A0A;
            background-image: url('Globe.png');
            background-position: center center;
            background-attachment: fixed;
            background-size: cover;
            background-repeat: no-repeat;
            color: var(--text-color);
            line-height: 1.6;
        }

        header {
            background: linear-gradient(135deg, var(--secondary-color), var(--background-color));
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
            border-bottom: 1px solid var(--border-color);
        }

        .header-content {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: 20px;
            flex-wrap: wrap;
            justify-content: flex-start;
        }

        .logo {
            width: clamp(40px, 8vw, 60px);
            height: clamp(40px, 8vw, 60px);
            border-radius: 50%;
            background: transparent;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
            overflow: hidden;
            flex-shrink: 0;
        }

        .logo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .header-text {
            text-align: left;
            flex: 1;
            min-width: 200px;
        }

        .header-text h1 {
            margin: 0;
            font-size: clamp(1.5rem, 4vw, 2.5rem);
            color: var(--primary-color);
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            line-height: 1.1;
        }

        .header-text p {
            margin: 0;
            font-size: clamp(0.9rem, 2vw, 1.2rem);
            color: var(--text-color);
            font-weight: 400;
        }

        .scrolling-text-container {
            background: var(--card-bg);
            padding: 15px 0;
            margin-top: 20px;
            border-top: 1px solid var(--border-color);
            overflow: hidden;
        }

        .scrolling-text {
            white-space: nowrap;
            animation: scroll 20s linear infinite;
            color: var(--primary-color);
            font-size: 1.5em;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 2px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .flying-icon {
            width: 116px;
            height: 75px;
            object-fit: contain;
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
        }

        @keyframes scroll {
            0% {
                transform: translateX(100%);
            }
            100% {
                transform: translateX(-100%);
            }
        }

        .token-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 20px 0;
            padding: 20px;
        }

        .section-title {
            color: var(--primary-color);
            font-size: 2em;
            margin: 40px 0 20px;
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .roadmap {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin: 20px 0;
        }

        .roadmap-item {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 10px;
            padding: 30px;
            position: relative;
            min-height: 250px;
        }

        .roadmap-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--primary-color);
            border-radius: 4px 0 0 4px;
        }

        .roadmap-phase {
            color: var(--primary-color);
            font-size: 1.2em;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .roadmap-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .roadmap-list li {
            margin: 10px 0;
            padding-left: 20px;
            position: relative;
        }

        .roadmap-list li::before {
            content: '•';
            color: var(--primary-color);
            position: absolute;
            left: 0;
        }

        .disclaimer {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 10px;
            padding: 40px;
            margin: 30px 0;
            font-size: 0.9em;
            line-height: 1.6;
            max-width: 1000px;
            margin: 30px auto;
        }

        .disclaimer ul {
            margin: 20px 0;
            padding-left: 20px;
        }

        .disclaimer li {
            margin: 10px 0;
        }

        .social-links {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin: 40px 0;
            flex-wrap: wrap;
        }

        .social-link {
            color: var(--text-color);
            text-decoration: none;
            padding: 12px 25px;
            border: 2px solid var(--primary-color);
            border-radius: 5px;
            transition: all 0.3s ease;
            min-width: 120px;
            text-align: center;
            font-weight: 500;
            background: transparent;
        }

        .social-link:hover {
            color: var(--secondary-color);
            background: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(255, 215, 0, 0.2);
        }

        .contract-info {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 10px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
            max-width: 800px;
            margin: 30px auto;
        }

        .contract-address {
            font-family: monospace;
            background: var(--background-color);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            word-break: break-all;
        }

        .stat-box {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            transition: transform 0.3s ease;
            min-height: 150px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2),
                        inset 0 1px 1px rgba(255, 255, 255, 0.05);
        }

        .stat-box:hover {
            transform: translateY(-5px);
        }

        .stat-value {
            font-size: 1.8em;
            color: var(--primary-color);
            font-weight: 700;
            margin: 10px 0;
        }

        .stat-label {
            color: var(--text-color);
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        main {
            max-width: 1400px;
            margin: 0 auto;
            padding: 40px 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        @media (max-width: 768px) {
            main {
                padding: 30px 15px;
            }
        }

        .content-box {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2),
                        inset 0 1px 1px rgba(255, 255, 255, 0.05);
            padding: 40px;
            margin: 20px 0;
            width: 100%;
            max-width: 1200px;
            box-sizing: border-box;
        }

        .banana-container {
            position: relative;
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 40px auto;
            transition: transform 0.3s ease;
            width: 100%;
            max-width: 500px;
            height: auto;
        }

        .banana {
            width: 100%;
            max-width: 500px;
            height: auto;
            object-fit: contain;
            filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.3));
            position: relative;
            z-index: 1;
            animation: zoomFadeIn 1.5s ease-out forwards;
            transition: transform 0.3s ease, filter 0.3s ease;
            cursor: pointer;
        }

        .banana:hover {
            transform: scale(1.1);
            filter: drop-shadow(0 8px 12px rgba(0, 0, 0, 0.4));
        }

        @keyframes zoomFadeIn {
            0% {
                opacity: 0;
                transform: scale(0.3);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }

        .allocation-table {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 15px;
            padding: 30px;
            margin: 30px auto;
            max-width: 1000px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2),
                        inset 0 1px 1px rgba(255, 255, 255, 0.05);
        }

        .allocation-table h3 {
            color: var(--primary-color);
            font-size: 1.8em;
            margin-bottom: 25px;
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .allocation-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 2fr;
            gap: 0;
            border: 1px solid var(--border-color);
            border-radius: 10px;
            overflow: hidden;
        }

        .allocation-header {
            background: linear-gradient(135deg, var(--primary-color), #ff8e8e);
            color: var(--secondary-color);
            padding: 15px;
            font-weight: 700;
            text-align: center;
            font-size: 0.95em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .allocation-row {
            display: contents;
        }

        .allocation-cell {
            padding: 15px;
            border-bottom: 1px solid var(--border-color);
            border-right: 1px solid var(--border-color);
            background: var(--card-bg);
            color: var(--text-color);
            font-size: 0.9em;
            display: flex;
            align-items: center;
        }

        .allocation-cell:last-child {
            border-right: none;
        }

        .allocation-row:last-child .allocation-cell {
            border-bottom: none;
        }

        .allocation-cell.emoji {
            justify-content: flex-start;
            font-size: 1.2em;
            padding-left: 20px;
        }

        .allocation-cell.percentage {
            justify-content: center;
            font-weight: 600;
            color: var(--primary-color);
        }

        .allocation-cell.tokens {
            justify-content: center;
            font-weight: 500;
        }

        .allocation-cell.description {
            padding-left: 20px;
        }

        .total-row .allocation-cell {
            background: linear-gradient(135deg, rgba(255, 107, 107, 0.1), rgba(255, 142, 142, 0.1));
            font-weight: 700;
            color: var(--primary-color);
        }

        @media (max-width: 900px) {
            .allocation-grid {
                display: block;
            }

            .allocation-header {
                display: none;
            }

            .allocation-row {
                display: block;
                margin-bottom: 20px;
                padding: 20px;
                background: var(--card-bg);
                border: 1px solid var(--border-color);
                border-radius: 10px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            .allocation-cell {
                display: block;
                padding: 10px 0;
                border: none;
                border-bottom: 1px solid var(--border-color);
                background: transparent;
                margin-bottom: 0;
                text-align: left;
                position: relative;
                padding-left: 100px;
            }

            .allocation-cell:last-child {
                border-bottom: none;
            }

            .allocation-cell::before {
                content: attr(data-label);
                font-weight: 700;
                color: var(--primary-color);
                position: absolute;
                left: 0;
                top: 10px;
                width: 90px;
                text-align: left;
            }

            .allocation-cell.emoji::before {
                content: "";
            }

            .allocation-cell.percentage::before {
                content: "Supply:";
            }

            .allocation-cell.tokens::before {
                content: "Tokens:";
            }

            .allocation-cell.description::before {
                content: "Purpose:";
            }

            .allocation-cell.description {
                padding-left: 100px;
                text-align: left;
                line-height: 1.4;
            }

            .allocation-cell.description::before {
                top: 10px;
            }
        }

        .chart-container {
            width: 100%;
            max-width: 900px;
            margin: 20px auto;
            padding: 30px;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
        }

        .chart-legend {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 30px;
            padding: 30px;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 1100px;
            margin: 30px auto;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.85em;
            color: var(--text-color);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .legend-color {
            width: 15px;
            height: 15px;
            border-radius: 3px;
        }

        .token-description {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 10px;
            padding: 30px;
            margin: 30px auto;
            max-width: 1000px;
            line-height: 1.8;
        }

        .token-description h2 {
            color: var(--primary-color);
            font-size: 1.8em;
            margin-bottom: 20px;
            text-align: center;
        }

        .token-description p {
            margin-bottom: 15px;
            font-size: 1.1em;
            color: var(--text-color);
        }

        .token-description p:last-child {
            margin-bottom: 0;
        }

        footer {
            background-color: var(--secondary-color);
            color: var(--text-color);
            padding: 30px 20px;
            text-align: center;
            margin-top: 60px;
            border-top: 1px solid var(--border-color);
        }

        .nav-menu {
            display: flex;
            gap: 15px;
            margin-left: auto;
            flex-wrap: wrap;
            justify-content: flex-end;
        }

        .nav-item {
            color: var(--text-color);
            text-decoration: none;
            font-size: 1em;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 5px;
            transition: all 0.3s ease;
            border: 2px solid var(--primary-color);
            background: transparent;
            white-space: nowrap;
        }

        .nav-item:hover {
            color: var(--secondary-color);
            background: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(255, 215, 0, 0.2);
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                text-align: center;
                gap: 20px;
                padding: 10px;
            }

            .logo-container {
                gap: 10px;
                justify-content: center;
            }

            .header-text {
                width: 100%;
                text-align: center;
            }

            .scrolling-text {
                font-size: 1.2em;
            }

            .banana {
                max-width: 300px;
            }

            .content-box {
                padding: 20px;
            }

            .token-stats {
                grid-template-columns: 1fr;
            }

            .roadmap {
                grid-template-columns: 1fr;
            }

            .social-links {
                gap: 15px;
                flex-direction: column;
                align-items: center;
            }

            .social-link {
                width: 90%;
                max-width: 300px;
                padding: 12px 20px;
                border-width: 1px;
                text-align: center;
            }

            .nav-menu {
                margin: 20px 0 0;
                justify-content: center;
                gap: 10px;
            }

            .nav-item {
                font-size: 0.95em;
                padding: 6px 12px;
                border-width: 1px;
            }
        }

        .scroll-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 50px;
            height: 50px;
            background: rgba(255, 215, 0, 0.2);
            border: 2px solid var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            opacity: 0;
            visibility: hidden;
            z-index: 1000;
        }

        .scroll-to-top.visible {
            opacity: 1;
            visibility: visible;
        }

        .scroll-to-top:hover {
            background: var(--primary-color);
            transform: translateY(-5px);
            box-shadow: 0 4px 8px rgba(255, 215, 0, 0.3);
        }

        .scroll-to-top::before {
            content: '↑';
            color: var(--primary-color);
            font-size: 24px;
            font-weight: bold;
            transition: color 0.3s ease;
        }

        .scroll-to-top:hover::before {
            color: var(--secondary-color);
        }

        @media (max-width: 768px) {
            .scroll-to-top {
                bottom: 20px;
                right: 20px;
                width: 40px;
                height: 40px;
            }

            .scroll-to-top::before {
                font-size: 20px;
            }
        }

        .footer-nav {
            display: flex;
            justify-content: center;
            gap: 0;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .footer-nav-item {
            color: var(--text-color);
            text-decoration: none;
            font-size: 1em;
            font-weight: 500;
            padding: 0 8px;
            transition: color 0.3s ease;
            border: none;
            background: none;
            white-space: nowrap;
        }

        .footer-nav-item:hover {
            color: var(--primary-color);
        }

        .footer-nav-item:not(:last-child)::after {
            content: '|';
            color: var(--text-color);
            margin-left: 8px;
        }

        @media (max-width: 768px) {
            .footer-nav {
                gap: 0;
            }

            .footer-nav-item {
                font-size: 0.95em;
                padding: 0 6px;
            }

            .footer-nav-item:not(:last-child)::after {
                margin-left: 6px;
            }
        }

        /* Tablet Responsive Styles */
        @media (min-width: 769px) and (max-width: 1024px) {
            .header-content {
                padding: 15px;
            }

            .logo {
                width: clamp(45px, 6vw, 55px);
                height: clamp(45px, 6vw, 55px);
            }

            .header-text h1 {
                font-size: clamp(1.8rem, 3.5vw, 2.2rem);
            }

            .header-text p {
                font-size: clamp(1rem, 2.2vw, 1.1rem);
            }

            .content-box {
                padding: 35px;
                margin: 15px;
            }

            .token-stats {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 25px;
            }

            .roadmap {
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 25px;
            }

            .chart-container {
                padding: 25px;
            }

            .chart-legend {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                padding: 25px;
            }

            .banana {
                max-width: 400px;
            }

            .allocation-grid {
                grid-template-columns: 1fr 0.8fr 1fr 1.8fr;
                gap: 0;
            }

            .allocation-cell {
                padding: 10px;
                font-size: 0.8em;
            }

            .allocation-header {
                padding: 12px 10px;
                font-size: 0.85em;
            }

            .social-links {
                gap: 20px;
                flex-wrap: wrap;
                justify-content: center;
            }

            .social-link {
                min-width: 140px;
                padding: 12px 20px;
            }

            .nav-menu {
                gap: 12px;
            }

            .nav-item {
                font-size: 0.9em;
                padding: 8px 14px;
            }

            .section-title {
                font-size: 1.8em;
            }

            .scrolling-text {
                font-size: 1.3em;
            }

            .flying-icon {
                width: 100px;
                height: 65px;
            }
        }

        /* Large Phone / Small Tablet Responsive Styles */
        @media (max-width: 900px) and (min-width: 769px) {
            .allocation-table {
                padding: 25px;
            }
        }

        /* Small Mobile Responsive Styles */
        @media (max-width: 480px) {
            .header-content {
                padding: 8px;
            }

            .content-box {
                padding: 15px;
                margin: 10px;
            }

            .token-stats {
                gap: 15px;
                padding: 15px;
            }

            .stat-box {
                padding: 20px;
                min-height: 120px;
            }

            .stat-value {
                font-size: 1.5em;
            }

            .section-title {
                font-size: 1.5em;
                margin: 30px 0 15px;
            }

            .banana {
                max-width: 250px;
            }

            .chart-container {
                padding: 15px;
            }

            .chart-legend {
                padding: 15px;
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .allocation-table {
                padding: 20px;
            }

            .social-links {
                gap: 10px;
                flex-direction: column;
                align-items: center;
            }

            .social-link {
                width: 90%;
                max-width: 280px;
                padding: 12px 15px;
            }

            .nav-menu {
                gap: 8px;
            }

            .nav-item {
                font-size: 0.85em;
                padding: 6px 10px;
            }

            .scrolling-text {
                font-size: 1em;
            }

            .flying-icon {
                width: 80px;
                height: 52px;
            }

            main {
                padding: 20px 10px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="header-content">
            <div class="logo-container">
                <div class="logo">
                    <img src="logo.png" alt="Krypto-Crypto Logo">
                </div>
                <div class="header-text">

                </div>
            </div>
            <nav class="nav-menu">
                <a href="#about" class="nav-item">About</a>
                <a href="#buy" class="nav-item">Buy Token</a>
                <a href="#social" class="nav-item">Social</a>
                <a href="#roadmap" class="nav-item">Roadmap</a>
                <a href="#tokenomics" class="nav-item">Tokenomics</a>
            </nav>
        </div>
        <div class="scrolling-text-container">
            <div class="scrolling-text">
                <img src="flying.png" alt="Flying" class="flying-icon">
                In Honor of Superman's dog, KRYPTO • And Cryptocurrency!• Let Krypto Crypto reign...
            </div>
        </div>
    </header>

    <main>
        <div class="content-box">
            <div class="token-stats">
                <div class="stat-box">
                    <div class="stat-value">$KRY</div>
                    <div class="stat-label">Token Symbol</div>
                </div>
                <div class="stat-box">
                    <div class="stat-value">1,000,000,000</div>
                    <div class="stat-label">Total Supply</div>
                </div>
                <div class="stat-box">
                    <div class="stat-value">0%</div>
                    <div class="stat-label">Transaction Tax</div>
                </div>
            </div>

            <div class="allocation-table">
                <h3>Token Allocation</h3>
                <div class="allocation-grid">
                    <div class="allocation-header">📊 Allocation</div>
                    <div class="allocation-header">% Supply</div>
                    <div class="allocation-header">Tokens</div>
                    <div class="allocation-header">Purpose</div>

                    <div class="allocation-row">
                        <div class="allocation-cell emoji">🚀 Presale</div>
                        <div class="allocation-cell percentage">65%</div>
                        <div class="allocation-cell tokens">650,000,000</div>
                        <div class="allocation-cell description">Raise $700K – get in early and ride the wave!</div>
                    </div>

                    <div class="allocation-row">
                        <div class="allocation-cell emoji">🧪 Launch (DEX)</div>
                        <div class="allocation-cell percentage">4.64%</div>
                        <div class="allocation-cell tokens">46,417,137</div>
                        <div class="allocation-cell description">$200K liquidity @ launch for moon fuel 🌙</div>
                    </div>

                    <div class="allocation-row">
                        <div class="allocation-cell emoji">🏛️ CEX Listing</div>
                        <div class="allocation-cell percentage">10%</div>
                        <div class="allocation-cell tokens">100,000,000</div>
                        <div class="allocation-cell description">Top exchange listings, max exposure 🔥</div>
                    </div>

                    <div class="allocation-row">
                        <div class="allocation-cell emoji">🐾 Team</div>
                        <div class="allocation-cell percentage">7.5%</div>
                        <div class="allocation-cell tokens">75,000,000</div>
                        <div class="allocation-cell description">Builders and believers</div>
                    </div>

                    <div class="allocation-row">
                        <div class="allocation-cell emoji">📣 Marketing</div>
                        <div class="allocation-cell percentage">6.5%</div>
                        <div class="allocation-cell tokens">65,000,000</div>
                        <div class="allocation-cell description">Memes, trends, viral raids</div>
                    </div>

                    <div class="allocation-row">
                        <div class="allocation-cell emoji">📢 KOL's</div>
                        <div class="allocation-cell percentage">3.5%</div>
                        <div class="allocation-cell tokens">35,000,000</div>
                        <div class="allocation-cell description">Influencer hype squad 💖</div>
                    </div>

                    <div class="allocation-row">
                        <div class="allocation-cell emoji">🎁 Airdrop</div>
                        <div class="allocation-cell percentage">2.86%</div>
                        <div class="allocation-cell tokens">28,582,863</div>
                        <div class="allocation-cell description">For the loyal community and meme lords 🎭</div>
                    </div>

                    <div class="allocation-row total-row">
                        <div class="allocation-cell emoji">📊 TOTAL</div>
                        <div class="allocation-cell percentage">100%</div>
                        <div class="allocation-cell tokens">1,000,000,000</div>
                    </div>
                </div>
            </div>

            <div class="banana-container">
                <img src="krypto.png" alt="Famous banana" class="banana">
            </div>

            <div id="about" class="token-description">
                <h2>About Krypto Crypto</h2>
                <p>Krypto Crypto is a meme token powered by community spirit, comic book nostalgia, and a love for all things Superdog and cryptocurrency. Inspired by Superman’s loyal canine companion, Krypto, this token isn’t just here to fetch hype — it’s here to fly!</p>
                <p>We’re blending the world of crypto with the charm of the DC Universe’s most heroic pup. Whether you’re a dog lover, or just along for the meme-ride, Krypto Crypto is built for fun, fairness, and a fandom that knows no bounds.</p>
                <p>No promises of saving the planet (yet), but with a strong community and some kryptonian-level vibes, we just might take this token to the moon — or the Fortress of Solitude.</p>

            </div>

            <div id="buy" class="contract-info">
                <h3>Contract Address</h3>
                <div class="contract-address">0x1234...5678</div>
                <p>Always verify the contract address before trading!</p>
            </div>

            <div id="social" class="social-links">
                <a href="#" class="social-link">Twitter</a>
                <a href="#" class="social-link">Telegram</a>
                <a href="#" class="social-link">Buy Token</a>
                <a href="#" class="social-link">Join Presale</a>
            </div>
        </div>

        <div id="tokenomics" class="content-box">
            <h2 class="section-title">Tokenomics</h2>
            <div class="chart-container">
                <canvas id="tokenDistribution"></canvas>
            </div>
            <p style="text-align: center;">All unsold presale tokens will be burned!</p>
            <div class="chart-legend" id="chartLegend"></div>
        </div>

        <div id="roadmap" class="content-box">
            <h2 class="section-title">Roadmap</h2>
            <div class="roadmap">
                <div class="roadmap-item">
                    <div class="roadmap-phase">Phase 1: Launch</div>
                    <ul class="roadmap-list">
                        <li>Website Launch</li>
                        <li>Community Building</li>
                        <li>Token Launch</li>
                        <li>Initial Marketing Push</li>
                    </ul>
                </div>
                <div class="roadmap-item">
                    <div class="roadmap-phase">Phase 2: Growth</div>
                    <ul class="roadmap-list">
                        <li>CEX Listings</li>
                        <li>Partnership Announcements</li>
                        <li>Community Events</li>
                        <li>Marketing Expansion</li>
                    </ul>
                </div>
                <div class="roadmap-item">
                    <div class="roadmap-phase">Phase 3: Expansion</div>
                    <ul class="roadmap-list">
                        <li>Major Exchange Listings</li>
                        <li>Ecosystem Development</li>
                        <li>Global Marketing Campaign</li>
                        <li>Future Announcements</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="content-box">
            <h2 class="section-title">Disclaimer</h2>
            <div class="disclaimer">
                <p>KRY Token is a meme token created for entertainment purposes only. This website and its contents are not financial advice. Cryptocurrency investments are inherently risky and volatile. Never invest more than you can afford to lose.</p>
                <p>By accessing this website and/or purchasing KRY tokens, you acknowledge and agree that:</p>
                <ul>
                    <li>You understand the risks associated with cryptocurrency investments</li>
                    <li>You are responsible for your own investment decisions</li>
                    <li>You have conducted your own research</li>
                    <li>You understand that the value of KRY tokens can go down as well as up</li>
                    <li>You are aware of the high volatility in cryptocurrency markets</li>
                </ul>
                <p>Always do your own research (DYOR) before making any investment decisions.</p>
            </div>
        </div>
    </main>

    <footer>
        <nav class="footer-nav">
            <a href="#about" class="footer-nav-item">About</a>
            <a href="#buy" class="footer-nav-item">Buy Token</a>
            <a href="#social" class="footer-nav-item">Social</a>
            <a href="#roadmap" class="footer-nav-item">Roadmap</a>
            <a href="#tokenomics" class="footer-nav-item">Tokenomics</a>
        </nav>
        <p>&copy; 2025 Krypto Crypto (KRY). All Rights Reserved.</p>
    </footer>

    <div class="scroll-to-top" id="scrollToTop" title="Scroll to top">↑</div>

    <script>
        const ctx = document.getElementById('tokenDistribution').getContext('2d');

        const data = {
            labels: ['🐾Team of Degens', '📣Meme Marketing', '📢KOL Shills', '🚀Presale Army', '🧪Launch Juice DEX', '🏛️CEX Invasion', '🎁Airdrop'],
            datasets: [{
                data: [7.5, 6.5, 3.5, 65, 4.64, 10, 2.86],
                backgroundColor: [
                    '#FF6B6B',
                    '#4ECDC4',
                    '#45B7D1',
                    '#96CEB4',
                    '#FFEEAD',
                    '#D4A5A5',
                    '#9B59B6'
                ],
                borderWidth: 0,
                hoverOffset: 15
            }]
        };

        const config = {
            type: 'pie',
            data: data,
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        enabled: true,
                        callbacks: {
                            title: function() {
                                return '';
                            },
                            label: function(context) {
                                return `${context.label} (${context.raw}%)`;
                            }
                        }
                    }
                },
                animation: {
                    animateScale: true,
                    animateRotate: true
                }
            }
        };

        const chart = new Chart(ctx, config);

        // Create custom legend
        const legendContainer = document.getElementById('chartLegend');
        data.labels.forEach((label, index) => {
            const legendItem = document.createElement('div');
            legendItem.className = 'legend-item';
            legendItem.innerHTML = `
                <div class="legend-color" style="background-color: ${data.datasets[0].backgroundColor[index]}"></div>
                <span>${label} (${data.datasets[0].data[index]}%)</span>
            `;
            legendContainer.appendChild(legendItem);
        });

        // Scroll to top functionality
        const scrollToTop = document.getElementById('scrollToTop');

        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                scrollToTop.classList.add('visible');
            } else {
                scrollToTop.classList.remove('visible');
            }
        });

        scrollToTop.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    </script>
</body>
</html>
